{"name": "react-shuhari", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "framer-motion": "^12.23.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "remark-gfm": "^4.0.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/postcss": "^4.1.11", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "vite": "^7.0.4", "vitest": "^3.2.4"}}